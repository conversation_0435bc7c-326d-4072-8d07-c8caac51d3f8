-- 测试用户信息更新功能的SQL脚本
-- 用于创建测试数据

-- 插入一条status=2（已拒绝）的用户信息更新记录
-- 请将 'YOUR_USER_ID' 替换为实际的用户ID
INSERT INTO base_user_info_registry (
    f_id,
    original_user_id,
    real_name,
    age,
    gender,
    mobile_phone,
    identification_number,
    emergency_contacts,
    emergency_contacts_phone,
    native_place,
    nation,
    home_address,
    political_outlook,
    education,
    specialty,
    graduation_school,
    graduation_time,
    join_work_time,
    seniority,
    marital_status,
    organize_name,
    position_name,
    position_sequence,
    category_personnel,
    form_employment,
    source_unit,
    go_project_time,
    remark,
    status,
    reviewer_id,
    reviewer_name,
    review_time,
    review_comment,
    f_creator_time,
    f_creator_user_id,
    f_last_modify_time,
    f_last_modify_user_id,
    f_delete_mark,
    f_delete_time,
    f_delete_user_id
) VALUES (
    'test-user-info-001',  -- f_id
    'YOUR_USER_ID',        -- original_user_id (请替换为实际用户ID)
    '测试用户',             -- real_name
    30,                    -- age
    1,                     -- gender (1-男)
    '13800138000',         -- mobile_phone
    '110101199001011234',  -- identification_number
    '紧急联系人',           -- emergency_contacts
    '13900139000',         -- emergency_contacts_phone
    '北京市',               -- native_place
    '汉族',                 -- nation
    '北京市朝阳区某某街道',   -- home_address
    '群众',                 -- political_outlook
    '大学本科',             -- education
    '计算机科学与技术',      -- specialty
    '北京大学',             -- graduation_school
    '2015-06-30',          -- graduation_time
    '2015-07-01',          -- join_work_time
    8,                     -- seniority
    '已婚',                 -- marital_status
    '技术部',               -- organize_name
    '软件工程师',           -- position_name
    '技术序列',             -- position_sequence
    '技术人员',             -- category_personnel
    '正式员工',             -- form_employment
    '社会招聘',             -- source_unit
    '2023-01-01',          -- go_project_time
    '测试用户信息更新功能',   -- remark
    2,                     -- status (2-已拒绝，这是关键字段)
    'admin',               -- reviewer_id
    '管理员',               -- reviewer_name
    NOW(),                 -- review_time
    '信息不完整，请重新填写', -- review_comment
    NOW(),                 -- f_creator_time
    'YOUR_USER_ID',        -- f_creator_user_id (请替换为实际用户ID)
    NOW(),                 -- f_last_modify_time
    'admin',               -- f_last_modify_user_id
    0,                     -- f_delete_mark
    NULL,                  -- f_delete_time
    NULL                   -- f_delete_user_id
);

-- 查询验证数据是否插入成功
-- SELECT * FROM base_user_info_registry WHERE original_user_id = 'YOUR_USER_ID' ORDER BY f_creator_time DESC;

-- 测试完成后清理数据
-- DELETE FROM base_user_info_registry WHERE f_id = 'test-user-info-001';
