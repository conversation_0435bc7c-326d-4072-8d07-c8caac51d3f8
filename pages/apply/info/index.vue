<template>
	<view class="jnpf-wrap personalData">
		<view style="background-color: #fff;" class="u-p-l-20 u-p-r-20">
			<u-form :model="dataForm" :errorType="['toast']" label-position="left" label-width="150" label-align="right"
				ref="dataForm">
				<u-form-item label="姓名" prop='realName' required>
					<u-input input-align='right' v-model="dataForm.realName" placeholder="请输入我的姓名"></u-input>
				</u-form-item>
				<!-- <u-form-item label="部门" prop="depSelect" required>
					<jnpf-postordep-select type="department" v-model="dataForm.organizeId"></jnpf-postordep-select>
				</u-form-item> -->
				<u-form-item label="民族" required>
					<jnpf-select v-model="dataForm.nation" placeholder="请选择我的民族" :options='nationOptions'>
					</jnpf-select>
				</u-form-item>
				<u-form-item label="性别" required>
					<jnpf-select v-model="dataForm.gender" placeholder="请选择我的性别" :options='genderOptions'
						:props='props'>
					</jnpf-select>
				</u-form-item>
				<u-form-item label="血型" required>
					<u-input input-align='right' v-model="dataForm.bloodType" placeholder="请输入血型"></u-input>
				</u-form-item>
				<u-form-item label="籍贯" required>
					<u-input input-align='right' v-model="dataForm.nativePlace" placeholder="请输入我的籍贯"></u-input>
				</u-form-item>
				<!-- <u-form-item label="身份证号" required>
					<u-input input-align='right' v-model="dataForm.identificationNumber" placeholder="请输入身份证号码">
					</u-input>
				</u-form-item> -->
				<u-form-item label="证件类型" required>
					<jnpf-select v-model="dataForm.certificatesType" placeholder="请选择证件类型"
						:options='certificatesTypeOptions'>
					</jnpf-select>
				</u-form-item>
				<u-form-item label="证件号码" required>
					<u-input input-align='right' v-model="dataForm.identificationNumber" placeholder="请输入证件号码">
					</u-input>
				</u-form-item>
				<u-form-item label="文化程度">
					<jnpf-select v-model="dataForm.education" placeholder="请选择文化程度" :options='educationOptions'>
					</jnpf-select>
				</u-form-item>
				<u-form-item label="出生年月" required>
					<jnpf-date-time type="date" v-model="dataForm.birthday" placeholder="请选择出生年月" format="YYYY-MM-DD">
					</jnpf-date-time>
				</u-form-item>
				<u-form-item label="办公电话">
					<u-input input-align='right' v-model="dataForm.telePhone" placeholder="请输入办公电话">
					</u-input>
				</u-form-item>
				<u-form-item label="办公座机">
					<u-input input-align='right' v-model="dataForm.landline" placeholder="请输入办公座机">
					</u-input>
				</u-form-item>
				<u-form-item label="手机号码" required>
					<u-input input-align='right' v-model="dataForm.mobilePhone" placeholder="请输入手机号码">
					</u-input>
				</u-form-item>
				<u-form-item label="电子邮箱">
					<u-input input-align='right' v-model="dataForm.email" placeholder="请输入电子邮箱">
					</u-input>
				</u-form-item>
				<u-form-item label="紧急联系人" required>
					<u-input input-align='right' v-model="dataForm.urgentContacts" placeholder="请输入紧急联系人">
					</u-input>
				</u-form-item>
				<u-form-item label="紧急电话" required>
					<u-input input-align='right' v-model="dataForm.urgentTelePhone" placeholder="请输入紧急电话">
					</u-input>
				</u-form-item>
				<u-form-item label="通讯地址">
					<u-input input-align='right' v-model="dataForm.postalAddress" placeholder="请输入通讯地址">
					</u-input>
				</u-form-item>
				<u-form-item label="自我介绍">
					<u-input input-align='right' v-model="dataForm.signature" placeholder="请输入自我介绍" type="textarea" />
				</u-form-item>
			</u-form>
		</view>
		<view class="flowBefore-actions">
			<template>
				<u-button class="buttom-btn" type="primary" @click='submit'>保存</u-button>
			</template>
		</view>
	</view>
</template>

<script>
	import {
		UpdateUser,
		UserSettingInfo
	} from '@/api/common'
	export default {
		data() {
			const data = {
				show: false,
				avatar: 'https://cdn.uviewui.com/uview/common/logo.png',
				props: {
					label: 'fullName',
					value: 'enCode'
				},
				dataForm: {
					birthday: null,
					certificatesNumber: "",
					certificatesType: "",
					education: "",
					email: "",
					gender: "",
					landline: "",
					mobilePhone: "",
					nation: '汉族',
					nativePlace: "",
					postalAddress: "",
					realName: "",
					signature: null,
					telePhone: "",
					urgentContacts: "",
					urgentTelePhone: "",
					id: null,
					identificationNumber:"",
					organizeId:"",
					bloodType:"",
				},
				nationOptions: [],
				genderOptions: [],
				certificatesTypeOptions: [],
				educationOptions: [],
				rules: {
					realName: [{
						required: true,
						message: '请输入姓名',
						trigger: ['change', 'blur'],
					}]
				},
				userInfo: {},
			}
			return data
		},
		onLoad() {
			this.getOptions()
			// 初始化从server获取用户信息
			UserSettingInfo().then(res => {
				this.userInfo = res.data || {}
				// 将用户信息赋值到表单中
				this.dataForm = {
					realName: this.userInfo.realName,
					gender: this.userInfo.gender,
					// TODO 补全下面的用户字段
					birthday: this.userInfo.birthday,
					certificatesNumber: this.userInfo.certificatesNumber,
					certificatesType: this.userInfo.certificatesType,
					education: this.userInfo.education,
					email: this.userInfo.email,
					landline: this.userInfo.landline,
					mobilePhone: this.userInfo.mobilePhone,
					nation: this.userInfo.nation,
					nativePlace: this.userInfo.nativePlace,
					postalAddress: this.userInfo.postalAddress,
					signature: this.userInfo.signature,
					telePhone: this.userInfo.telePhone,
					urgentContacts: this.userInfo.urgentContacts,
					urgentTelePhone: this.userInfo.urgentTelePhone,
					id: this.userInfo.id,
					identificationNumber:this.userInfo.identificationNumber,
					organizeId:this.userInfo.organizeId,
					bloodType:this.userInfo.bloodType,
				}
			})
		},
		computed: {
			baseURL() {
				return this.define.baseURL
			},
		},
		mounted() {
			this.$refs.dataForm.setRules(this.rules);
		},
		methods: {
			init(data) {
				let initData = JSON.parse(JSON.stringify(data))
				for (let key in initData) {
					for (let k in this.dataForm) {
						if (key === k) {
							this.dataForm[key] = initData[key]
						}
					}
				}
			},
			getOptions() {
				this.$store.dispatch('base/getDictionaryData', {
					sort: 'Education'
				}).then((res) => {
					this.educationOptions = JSON.parse(JSON.stringify(res))
					this.educationOptions.map((o) => {
						if (o.id === this.dataForm.education) {
							this.dataForm.education = o.id
						}
					})
					this.$store.dispatch('base/getDictionaryData', {
						sort: 'certificateType'
					}).then((res) => {
						this.certificatesTypeOptions = JSON.parse(JSON.stringify(res))
						this.certificatesTypeOptions.map((o) => {
							if (o.id === this.dataForm.certificatesType) {
								this.dataForm.certificatesType = o.id
							}
						})
					})
					this.$store.dispatch('base/getDictionaryData', {
						sort: 'sex'
					}).then(res => {
						this.genderOptions = JSON.parse(JSON.stringify(res))
						this.genderOptions.map((o) => {
							if (o.enCode === this.dataForm.gender) {
								this.dataForm.gender = o.enCode
							}
						})
					})
					this.$store.dispatch('base/getDictionaryData', {
						sort: 'Nation'
					}).then(res => {
						this.nationOptions = JSON.parse(JSON.stringify(res))
						this.nationOptions.map((o) => {
							if (o.id === this.dataForm.nation) {
								this.dataForm.nation = o.id
							}
						})
					})
				})
				this.show = true
			},
			submit() {
				this.$refs.dataForm.validate(valid => {
					if (valid) {
						UpdateUser(this.dataForm).then(res => {
							uni.showToast({
								title: '保存成功',
								duration: 800,
								icon: 'none'
							});
							setTimeout(() => {
								uni.navigateBack()
							}, 1000)
						})
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f0f2f6;
	}

	.slot-btn {
		width: 329rpx;
		height: 240rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: rgb(244, 245, 246);
		border-radius: 10rpx;
	}

	.slot-btn__hover {
		background-color: rgb(235, 236, 238);
	}
</style>