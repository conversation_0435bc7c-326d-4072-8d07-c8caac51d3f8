{"id": "qiun-data-charts", "displayName": "秋云 ucharts echarts 高性能跨全端图表组件", "version": "2.5.0-20230101", "description": "uCharts 新增正负柱状图！支持H5及APP用 ucharts echarts 渲染图表，uniapp可视化首选组件", "keywords": ["ucharts", "echarts", "f2", "图表", "可视化"], "repository": "https://gitee.com/uCharts/uCharts", "engines": {"HBuilderX": "^3.3.8"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "474119"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "https://www.npmjs.com/~qiun", "type": "component-vue"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}