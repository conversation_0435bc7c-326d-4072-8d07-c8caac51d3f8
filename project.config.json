{"description": "项目配置文件", "packOptions": {"ignore": []}, "setting": {"urlCheck": true, "es6": true, "enhance": false, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": false, "useApiHook": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "bundle": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true}, "compileType": "miniprogram", "libVersion": "2.17.0", "appid": "wxb96c6e15d662b114", "projectname": "miniprogram-1", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "isGameTourist": false, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}}