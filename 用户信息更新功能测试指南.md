# 用户信息更新功能测试指南

## 功能概述

在用户登录后，系统会自动检查该用户在 `base_user_info_registry` 表中的最新记录。如果存在状态为 2（已拒绝）的记录，系统会弹框提示"您的用户信息需要完善！"，点击确定后跳转到用户信息更新页面。

## 测试准备

### 1. 数据库准备

1. 执行 `test-user-info-update.sql` 脚本
2. 将脚本中的 `YOUR_USER_ID` 替换为要测试的实际用户ID
3. 执行SQL插入测试数据

### 2. 代码部署

确保以下文件已正确部署：
- `store/modules/user.js` - 包含检查逻辑
- `pages/apply/userInfoUpdate/index.vue` - 用户信息更新页面
- `api/basic/userInfoRegistry.js` - API接口文件

## 测试步骤

### 测试1：登录后弹框检查

1. **操作**：使用测试用户账号登录系统
2. **预期结果**：
   - 登录成功后，系统自动弹出提示框
   - 提示内容为："您的用户信息需要完善！"
   - 只有"确定"按钮，没有"取消"按钮

### 测试2：页面跳转

1. **操作**：在弹框中点击"确定"按钮
2. **预期结果**：
   - 弹框关闭
   - 页面跳转到 `/pages/apply/userInfoUpdate/index`
   - 显示用户信息更新表单

### 测试3：表单功能

1. **操作**：在用户信息更新页面填写表单
2. **测试内容**：
   - 必填字段验证（姓名、性别、手机号、身份证号）
   - 日期选择器功能（毕业时间、参加工作时间、进项目时间）
   - 表单数据预填充（如果有现有用户信息）

### 测试4：表单提交

1. **操作**：填写完整表单并点击"提交申请"
2. **预期结果**：
   - 显示"提交成功"提示
   - 数据保存到 `base_user_info_registry` 表
   - 新记录的 `status` 字段为 0（待审核）
   - 1.5秒后自动返回上一页

### 测试5：重复登录检查

1. **操作**：提交表单后，重新登录系统
2. **预期结果**：
   - 不再弹出提示框（因为最新记录状态不是2）

## 验证SQL

```sql
-- 查看用户的信息更新记录
SELECT * FROM base_user_info_registry 
WHERE original_user_id = 'YOUR_USER_ID' 
ORDER BY f_creator_time DESC;

-- 查看最新记录的状态
SELECT status, review_comment, f_creator_time 
FROM base_user_info_registry 
WHERE original_user_id = 'YOUR_USER_ID' 
ORDER BY f_creator_time DESC 
LIMIT 1;
```

## 可能的问题和解决方案

### 问题1：登录后没有弹框
**可能原因**：
- 数据库中没有status=2的记录
- 用户ID不匹配
- API调用失败

**解决方案**：
- 检查数据库数据
- 查看浏览器控制台错误信息
- 确认API接口路径正确

### 问题2：页面跳转失败
**可能原因**：
- 页面路径错误
- 页面文件不存在

**解决方案**：
- 确认页面文件存在于正确路径
- 检查路由配置

### 问题3：表单提交失败
**可能原因**：
- API接口问题
- 数据验证失败
- 网络问题

**解决方案**：
- 检查API接口是否正常
- 查看网络请求和响应
- 确认表单数据格式正确

## 清理测试数据

测试完成后，执行以下SQL清理测试数据：

```sql
DELETE FROM base_user_info_registry WHERE f_id = 'test-user-info-001';
```

## 注意事项

1. 测试时请使用测试环境，避免影响生产数据
2. 测试完成后及时清理测试数据
3. 如果发现问题，请记录详细的错误信息和复现步骤
4. 建议在不同浏览器和设备上进行测试，确保兼容性
